<?php

/**
 * 全局配置模型类
 * @desc 全局配置模型类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/06/09
 */

namespace App\Models\MySQL;

use DateTimeInterface;

class GlobalConfig extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'global_config';

    /**
     * 主键字段
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 允许写入的字段
     *
     * @var array
     */
    protected $fillable = [
        'developer_app_id', // 研发效能APP项目id
        'config_data',      // 配置数据(JSON格式)
    ];

    /**
     * 字段类型转换
     *
     * @var array
     */
    protected $casts = [
        'config_data' => 'json', // 使用json类型而不是array
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 默认配置填充值
     * 当应用没有配置时返回的默认值
     */
    const FILL_DEFAULT_CONFIG = [
        'config_data' => [
            'notify_server_url' => '',
        ],
    ];

    /**
     * 日期格式化
     *
     * @param DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }
}
