<?php

/**
 * 外挂数据处理服务类
 * @desc 外挂数据处理服务类
 * <AUTHOR> ch<PERSON><PERSON><PERSON><PERSON><PERSON>@shiyue.com
 * @date 2025/01/09
 * @todo 这里是后续需要跟进的功能说明
 */

namespace App\Services\Plugin;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ExternalDataProcessService extends BaseService
{
    /**
     * 缓存前缀
     *
     * @var string
     */
    const CACHE_PREFIX = 'external_data_';

    /**
     * 缓存时间（2小时）
     *
     * @var int
     */
    const CACHE_TTL = 7200;

    /**
     * 风险等级关键词映射
     *
     * @var array
     */
    const RISK_KEYWORDS = [
        '该玩家疑似开挂' => self::MIDDLE_RISK,
        '该玩家开挂修改伤害' => self::HIGH_RISK,
        '该玩家开挂加速' => self::HIGH_RISK
    ];

    /**
     * 处理外挂数据
     *
     * @param array $data
     * @return array
     */
    public function processData(array $data): array
    {
        try {
            // 1. 生成session_id
            $sessionId = $this->generateSessionId($data);

            // 2. 检查缓存，避免重复处理
            $cacheKey = self::CACHE_PREFIX . $sessionId;
            if (Cache::has($cacheKey)) {
                Log::info("外挂数据已处理，跳过", ['session_id' => $sessionId]);
                return ['status' => 'skipped', 'session_id' => $sessionId];
            }

            // 3. 获取风险等级
            $riskLevel = $this->getRiskLevel($data['explain_desc'] ?? '');

            // 4. 风险等级为0时跳过处理
            if ($riskLevel == 0) {
                Log::info("风险等级为0，跳过处理", ['session_id' => $sessionId]);
                return ['status' => 'skipped', 'session_id' => $sessionId, 'reason' => 'risk_level_zero'];
            }

            // 5. 写入初始化数据
            $this->writeExternalInitData($sessionId, $data);

            // 6. 写入上传数据
            $this->writeExternalUploadData($sessionId, $data, $riskLevel);

            // 7. 高风险时写入命中数据
            if ($riskLevel == 3) {
                $this->writeExternalHitData($sessionId, $data, $riskLevel);
            }

            // 8. 设置缓存
            Cache::put($cacheKey, 1, self::CACHE_TTL);

            Log::info("外挂数据处理完成", [
                'session_id' => $sessionId,
                'risk_level' => $riskLevel
            ]);

            return [
                'status' => 'processed',
                'session_id' => $sessionId,
                'risk_level' => $riskLevel
            ];

        } catch (\Exception $e) {
            Log::error("外挂数据处理失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * 生成session_id
     *
     * @param array $data
     * @return string
     */
    protected function generateSessionId(array $data): string
    {
        $string = $data['dev_create_time'] . $data['server_dev_str'] . $data['extra_app_id'] . $data['os_type'];
        return md5($string);
    }

    /**
     * 获取风险等级
     *
     * @param string $explainDesc
     * @return int
     */
    public function getRiskLevel(string $explainDesc): int
    {
        foreach (self::RISK_KEYWORDS as $keyword => $level) {
            if (strpos($explainDesc, $keyword) !== false) {
                return self::RISK_LEVEL_MAP[$level];
            }
        }
        return 0;
    }

    /**
     * 写入外挂初始化数据
     *
     * @param string $sessionId
     * @param array $data
     * @return void
     */
    public function writeExternalInitData(string $sessionId, array $data): void
    {
        $initData = [
            'android_id' => $data['server_dev_str'],
            'app_name' => $data['app_name'] ?? '',
            'app_package_name' => $data['sdk_package_name'],
            'app_version' => $data['app_version'],
            'cpu_framework' => $data['rom_info'] ?? '',
            'stream_date' => date('Y-m-d H:i:s', $data['dev_create_time']),
            'device_name' => $data['manufacturer'] . ' ' . $data['device_model'],
            'device_brand' => $data['manufacturer'],
            'device_manufacturer' => $data['manufacturer'],
            'device_model' => $data['device_model'],
            'extra_app_id' => $data['extra_app_id'],
            'ip' => $data['ip'],
            'is_emulator' => $data['is_emulator'],
            'is_root' => $data['is_root'] ?? '0',
            'network_type' => $data['network_type'] ?? '',
            'os_type' => $data['os_type'],
            'os_version' => $data['os_version'],
            'resolution' => str_replace('X', '*', $data['resolution'] ?? ''),
            'rom_info' => $data['rom_info'] ?? '',
            'sdk_ver' => $data['sdk_ver'] ?? '',
            'oaid' => $data['server_dev_str'],
            'server_dev_str' => $data['server_dev_str'],
            'session_id' => $sessionId,
            'slot_count' => '',
            'time_zone_id' => '',
            'ui_mode_type' => '',
            'vpn_address' => '',
            'app_process_name' => '',
            'app_sign_info' => '',
            'bluetooth_address' => '',
            'cpu_abis' => '',
            'cpu_cores' => '0',
            'cpu_cur' => '',
            'cpu_max' => '',
            'cpu_min' => '',
            'cpu_name' => '',
            'device_board' => '',
            'device_display' => '',
            'device_hardware' => '',
            'device_id' => '',
            'device_product' => '',
            'device_security_patch' => '',
            'device_serial_id' => '',
            'device_tags' => '',
            'device_time' => '',
            'device_type' => '',
            'dns' => '',
            'is_accessibility' => '0',
            'is_airplane_mode' => '0',
            'is_cloudPhone' => '0',
            'is_mock_location' => '0',
            'is_tablet' => '0',
            'is_use_debug' => '0',
            'is_using_vpn' => '0',
            'keyboard' => '0',
            'language' => '',
            'last_boot_time' => '',
            'locale_display_language' => '',
            'locale_iso_3_country' => '',
            'locale_iso_3_language' => '',
            'mac' => '',
            'mcc' => '',
            'mnc' => '',
            'network_operator' => '',
            'network_operator_name' => '',
            'phone_type' => '',
            'ringer_mode' => '2',
            'screen_density' => '',
            'screen_density_dpi' => '',
            'screen_physical_size' => '',
        ];

        $this->writeToFile('external-init-data', $initData);
    }

    /**
     * 写入外挂上传数据
     *
     * @param string $sessionId
     * @param array $data
     * @param int $riskLevel
     * @return void
     */
    public function writeExternalUploadData(string $sessionId, array $data, int $riskLevel): void
    {
        // 获取服务器ID和名称
        $serverId = $data['server_id'] ?? '';
        $serverName = $data['server_name'] ?? '';

        // 如果extra字段存在，从中提取serverID和serverName
        if (!empty($data['extra']) && is_array($data['extra'])) {
            foreach ($data['extra'] as $item) {
                if ($item['key'] == 'serverID') {
                    $serverId = $item['value'] ?? '';
                }
                if ($item['key'] == 'serverName') {
                    $serverName = $item['value'] ?? '';
                }
            }
        }

        $uploadData = [
            'application_info' => [],
            'click_info' => '',
            'extra_app_id' => $data['extra_app_id'],
            'game_info' => json_encode([
                'account_id' => $data['account_id'],
                'role_id' => $data['role_id'],
                'role_name' => $data['role_name'],
                'server_id' => $serverId,
                'server_name' => $serverName,
                'extension' => '',
            ], JSON_UNESCAPED_UNICODE),
            'ip' => $data['ip'],
            'is_accessibility' => 0,
            'is_plugin' => $riskLevel == 3 ? 1 : 0,
            'network_type' => $data['network_type'] ?? '',
            'os_type' => $data['os_type'],
            'port_info' => '',
            'report_interval' => 0,
            'risk_level' => $riskLevel,
            'server_dev_str' => $data['server_dev_str'],
            'session_id' => $sessionId,
            'socket_info' => '',
            'stream_date' => date('Y-m-d H:i:s', $data['dev_create_time']),
            'write_info' => '',
            'is_game_upload' => '1',
            'hitbug_explain_desc' => $data['explain_desc'] ?? '',
            'hitbug_exception_image' => $data['exception_image'] ?? '',
        ];

        $this->writeToFile('external-upload-data', $uploadData);
    }

    /**
     * 写入外挂命中数据
     *
     * @param string $sessionId
     * @param array $data
     * @param int $riskLevel
     * @return void
     */
    public function writeExternalHitData(string $sessionId, array $data, int $riskLevel): void
    {
        $hitData = [
            'session_id' => $sessionId,
            'stream_date' => date('Y-m-d H:i:s', $data['dev_create_time']),
            'port_info' => [],
            'socket_info' => [],
            'click_info' => json_encode(['click_num' => 0, 'match_num' => 0, 'ratio' => 0], JSON_UNESCAPED_UNICODE),
            'action' => '99',
            'title' => '',
            'extra_app_id' => $data['extra_app_id'],
            'server_dev_str' => $data['server_dev_str'],
            'ip' => $data['ip'],
            'account_id' => $data['account_id'],
            'application_info' => '',
            'write_info' => '',
            'phone_info' => json_encode([
                'is_dpi' => 0,
                'is_emulator' => $data['is_emulator'],
                'is_resolution' => 0,
                'is_root' => $data['is_root'] ?? '0',
            ], JSON_UNESCAPED_UNICODE),
            'risk_level' => $riskLevel,
        ];

        $this->writeToFile('external-hit-data', $hitData);
    }

    /**
     * 写入文件
     *
     * @param string $type
     * @param array $data
     * @return void
     */
    protected function writeToFile(string $type, array $data): void
    {
        $date = date('Ymd');
        $path = storage_path("app/{$type}/{$date}.log");

        // 判断目录是否存在，不存在就创建
        $dir = pathinfo($path)['dirname'];
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }

        // 追加的方式添加，每行一条数据
        file_put_contents($path, json_encode($data, JSON_UNESCAPED_UNICODE) . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
}
