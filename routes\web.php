<?php

use App\Http\Controllers\ApiController;
use App\Http\Controllers\BlacklistController;
use App\Http\Controllers\OverviewController;
use App\Http\Controllers\PluginController;
use App\Http\Controllers\WhitelistController;
use App\Http\Controllers\SwitchConfigController;
use App\Http\Controllers\GlobalConfigController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return 'fail';
});

Route::group(['middleware' => 'auth'], function () {
    //外挂检测报告数据相关
    Route::group(['prefix' => '/plugin'], function () {
        //列表
        Route::post('list', [PluginController::class, 'list']);
        //详情
        Route::post('detail', [PluginController::class, 'detail']);
        //搜索
        Route::post('search', [PluginController::class, 'search']);
        // 导出数据
        Route::post('export/data', [PluginController::class, 'exportData']);
        // 导出状态
        Route::get('export/status', [PluginController::class, 'exportStatus']);
        // 下载
        Route::get('export/download', [PluginController::class, 'exportDownload']);
    });
    //概览相关数据
    Route::group(['prefix' => '/overview'], function () {
        // 概览页-数据总览
        Route::get('summary', [OverviewController::class, 'summary']);
        // 概览页-统计图
        Route::get('trend', [OverviewController::class, 'trend']);
        // 概览页-外挂设备品牌排行榜
        Route::get('chectDevBrandRank', [OverviewController::class, 'chectDevBrandRank']);
        // 概览页-外挂设备IP排行榜
        Route::get('chectDevIPRank', [OverviewController::class, 'chectDevIPRank']);
        // 概览页-外挂应用列表排行
        Route::get('chectAppRank', [OverviewController::class, 'chectAppRank']);
        // 概览页-外挂设备系统版本排行榜
        Route::get('chectDevSystemRank', [OverviewController::class, 'chectDevSystemRank']);
        // 概览页-外挂设备执行动作排行榜
        Route::get('chectDevActionRank', [OverviewController::class, 'chectDevActionRank']);
    });
    // 白名单管理
    Route::group(['prefix' => '/whitelist'], function () {
        // 列表
        Route::get('list', [WhitelistController::class, 'list']);
        // 添加
        Route::post('add', [WhitelistController::class, 'add']);
        // 编辑
        Route::post('edit', [WhitelistController::class, 'edit']);
        // 删除
        Route::get('delete', [WhitelistController::class, 'delete']);
    });
    // 黑名单管理
    Route::group(['prefix' => '/blacklist'], function () {
        // 列表
        Route::get('list', [BlacklistController::class, 'list']);
        // 添加
        Route::post('add', [BlacklistController::class, 'add']);
        // 编辑
        Route::post('edit', [BlacklistController::class, 'edit']);
        // 删除
        Route::get('delete', [BlacklistController::class, 'delete']);
    });
    // 配置页相关数据
    Route::group(['prefix' => '/config'], function () {
        // 配置页-开关配置信息
        Route::get('getConfigInfo', [SwitchConfigController::class, 'getConfigInfo']);
        // 配置页-开关配置编辑
        Route::post('edit', [SwitchConfigController::class, 'edit']);
    });
    // 全局配置相关数据
    Route::group(['prefix' => '/global-config'], function () {
        // 全局配置-获取配置信息
        Route::get('getConfigInfo', [GlobalConfigController::class, 'getConfigInfo']);
        // 全局配置-编辑配置
        Route::post('edit', [GlobalConfigController::class, 'edit']);
    });
});

Route::post('/plugin/process-data', [ApiController::class, 'processData']);
