# 全局配置API文档

## 项目介绍

全局配置功能允许每个应用(developer_app_id)存储和管理任意格式的JSON配置数据。该功能提供了灵活的配置管理能力，支持各种业务场景的配置需求。

### 功能特性

- **一应用一配置**：每个应用只有一条全局配置记录
- **JSON格式存储**：支持任意结构的JSON配置数据
- **简洁接口**：只提供获取和编辑两个核心接口
- **配置监控**：集成配置变更监控，记录所有配置修改操作
- **默认值支持**：应用首次访问时返回默认空配置

---

## API接口文档

### 1. 获取全局配置信息

**接口地址**：`GET /global-config/getConfigInfo`

**接口描述**：获取指定应用的全局配置信息，如果应用首次访问则返回默认空配置

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| developer_app_id | int | 是 | 研发效能APP项目ID，用于标识具体的应用 | 1 |

#### 请求示例

```json
{
  "developer_app_id": 1
}
```

#### 响应参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| code | int | 响应状态码，200表示成功 | 200 |
| message | string | 响应消息 | "success" |
| data | object | 配置数据对象 | - |
| data.id | int | 配置记录的唯一标识ID | 1 |
| data.developer_app_id | int | 应用ID | 1 |
| data.config_data | object | JSON格式的配置数据，支持任意结构 | {...} |
| data.created_at | string | 配置创建时间 | "2024-01-01 12:00:00" |
| data.updated_at | string | 配置最后更新时间 | "2024-01-01 12:00:00" |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "developer_app_id": 1,
    "config_data": {
      "url": "https://www.baidu.com",
      "status": 1,
      "timeout": 30,
      "custom_settings": {
        "feature_a": true,
        "feature_b": false
      }
    },
    "created_at": "2024-01-01 12:00:00",
    "updated_at": "2024-01-01 12:00:00"
  }
}
```

---

### 2. 编辑全局配置

**接口地址**：`POST /global-config/edit`

**接口描述**：新增或更新指定应用的全局配置信息，系统会自动判断是新增还是更新操作

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| developer_app_id | int | 是 | 研发效能APP项目ID，用于标识具体的应用 | 1 |
| config_data | object | 是 | JSON格式的配置数据，支持任意结构的配置内容 | {...} |

#### 请求示例

```json
{
  "developer_app_id": 1,
  "config_data": {
    "url": "https://www.baidu.com",
    "status": 1,
    "timeout": 30,
    "custom_settings": {
      "feature_a": true,
      "feature_b": false,
      "threshold": 100
    }
  }
}
```

#### 响应参数

| 参数名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| code | int | 响应状态码，200表示操作成功 | 200 |
| message | string | 响应消息 | "success" |
| data | array | 响应数据，编辑操作通常返回空数组 | [] |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": []
}
```

---

## 配置数据示例

### 基础配置示例

适用于简单的配置需求：

```json
{
  "url": "https://api.example.com",
  "status": 1,
  "timeout": 30
}
```

### 复杂配置示例

适用于多模块、多层级的配置需求：

```json
{
  "api_settings": {
    "base_url": "https://api.example.com",
    "timeout": 30,
    "retry_count": 3
  },
  "feature_flags": {
    "enable_cache": true,
    "enable_logging": false,
    "max_connections": 100
  },
  "business_config": {
    "payment_methods": ["alipay", "wechat", "bank"],
    "default_currency": "CNY",
    "tax_rate": 0.06
  }
}
```

---

## 错误码说明

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 404 | 接口不存在 | 检查接口地址是否正确 |
| 500 | 服务器内部错误 | 联系技术支持 |

---

## 使用说明

1. **首次使用**：应用首次调用获取配置接口时，会返回默认的空配置
2. **配置更新**：通过编辑接口可以新增或更新配置，系统会自动判断是新增还是更新操作
3. **配置监控**：所有配置变更都会被记录和监控，便于追踪配置变化
4. **数据格式**：配置数据必须是有效的JSON格式，支持嵌套结构

## 注意事项

- 每个应用只能有一条全局配置记录
- 配置数据必须是有效的JSON格式
- 所有配置变更都会被监控和记录
- 建议在配置中使用有意义的键名，便于维护和理解

---

**文档版本**：v1.0  
**最后更新**：2024-01-01  
**维护人员**：开发团队
